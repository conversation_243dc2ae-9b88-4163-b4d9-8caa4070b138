from transformers import pipeline
from data import coordination_mechanisms, processes, improved_project_resilience, community_satisfaction, monitoring_indicators, feedback_mechanisms, governance_rules, socio_ecological_challenges, stakeholders

# Load pre-trained transformer pipeline
classifier = pipeline("zero-shot-classification")


# Function to find the top matches
def find_top_matches(project_description, category_items, top_n=3):
    scored_items = []
    for item, description in category_items:
        result = classifier(project_description, candidate_labels=[item])
        score = result['scores'][0]
        scored_items.append((item, description, score))

    scored_items.sort(key=lambda x: x[2], reverse=True)
    return scored_items[:top_n]


def assess_project_recommendation(project_description):
    results = {
        "Top Coordination Mechanisms": {
                'recommendations': find_top_matches(project_description, coordination_mechanisms),
                'references': []    
            },
        "Top Processes": {
                'recommendations': find_top_matches(project_description, processes),
                'references': []    
            },
        "Top Improved Project Resilience Factors": {
                'recommendations': find_top_matches(project_description, improved_project_resilience),
                'references': []    
            },
        "Top Community Satisfaction Factors": {
                'recommendations': find_top_matches(project_description, community_satisfaction),
                'references': []    
            },
        "Top Monitoring Indicators": {
                'recommendations': find_top_matches(project_description, monitoring_indicators),
                'references': []    
            },
        "Top Feedback Mechanisms": {
                'recommendations': find_top_matches(project_description, feedback_mechanisms),
                'references': []    
            },
        "Top Governence rules": {
                'recommendations': find_top_matches(project_description, governance_rules),
                'references': []    
            },
        "Top Socio-ecological Challenges": {
                'recommendations': find_top_matches(project_description, socio_ecological_challenges),
                'references': []    
            },
        "Top Stakeholders": {
                'recommendations': find_top_matches(project_description, stakeholders),
                'references': []    
            },
    }

    for category, items in results.items():
        print(f"\n{category}:")
        for item, description, score in items['recommendations']:
            print(f" - {item}: {description} (Relevance Score: {score:.2f})")
            
    return results

