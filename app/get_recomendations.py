
from lib.recommend_processor import assess_project_recommendation
from lib.pdf_processor import PDFProcessor




def get_recomendations(body):
    project_description = body['project_description']
    pdf_link = body['pdf_link']
    
    
    try: 
        
        recommendations = assess_project_recommendation(project_description)
        pdf_processor = PDFProcessor(pdf_link, recommendations)
        pdf_processor.process_pdf()
        
        
    except Exception as e:
        print(f"Failed to process PDF: {e}")
    